package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// 测试用的快递鸟工单数据
type TestWorkOrder struct {
	KDNOrderCode string
	OrderCode    string
	TrackingNo   string
	Description  string
}

func main() {
	fmt.Println("=== 多个快递鸟工单回调测试 ===")
	fmt.Println("🔥 测试多个不同的快递鸟工单号")
	fmt.Println("📋 验证最简参数查询策略在不同工单上的表现")
	fmt.Println()

	// 🔥 真实的快递鸟工单数据（从数据库中提取）
	testWorkOrders := []TestWorkOrder{
		{
			KDNOrderCode: "KDN2508241510000912",
			OrderCode:    "GK20250824000002769",
			TrackingNo:   "773373636316658",
			Description:  "工单1 - 最新工单",
		},
		{
			KDNOrderCode: "KDN2508162010000116", 
			OrderCode:    "GK20250816000004460",
			TrackingNo:   "773372263791475",
			Description:  "工单2 - 8月16日工单",
		},
		{
			KDNOrderCode: "KDN2508241910000077",
			OrderCode:    "GK20250824000004337", 
			TrackingNo:   "773373672501336",
			Description:  "工单3 - 8月24日工单",
		},
		{
			KDNOrderCode: "KDN2508241310002713",
			OrderCode:    "GK20250824000002194",
			TrackingNo:   "773373617978637", 
			Description:  "工单4 - 8月24日工单",
		},
		{
			KDNOrderCode: "KDN2508160810000769",
			OrderCode:    "GK20250816000000348",
			TrackingNo:   "773372135742158",
			Description:  "工单5 - 8月16日工单",
		},
	}

	fmt.Printf("🔍 准备测试 %d 个不同的快递鸟工单:\n", len(testWorkOrders))
	for i, wo := range testWorkOrders {
		fmt.Printf("   %d️⃣ %s\n", i+1, wo.Description)
		fmt.Printf("      📋 KDN订单号: %s\n", wo.KDNOrderCode)
		fmt.Printf("      🎯 平台订单号: %s\n", wo.OrderCode)
		fmt.Printf("      📦 运单号: %s\n", wo.TrackingNo)
		fmt.Println()
	}

	fmt.Println("🚀 开始批量测试...")
	fmt.Println()

	successCount := 0
	for i, wo := range testWorkOrders {
		fmt.Printf("🔥 测试工单 %d/%d: %s\n", i+1, len(testWorkOrders), wo.Description)
		fmt.Printf("   📋 KDN订单号: %s\n", wo.KDNOrderCode)
		
		success := testWorkOrderCallback(wo)
		if success {
			successCount++
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		fmt.Println()
		
		// 等待2秒，避免请求过于频繁
		if i < len(testWorkOrders)-1 {
			fmt.Println("⏳ 等待2秒...")
			time.Sleep(2 * time.Second)
			fmt.Println()
		}
	}

	fmt.Println("=== 批量测试结果汇总 ===")
	fmt.Printf("📊 总测试数量: %d\n", len(testWorkOrders))
	fmt.Printf("✅ 成功数量: %d\n", successCount)
	fmt.Printf("❌ 失败数量: %d\n", len(testWorkOrders)-successCount)
	fmt.Printf("📈 成功率: %.1f%%\n", float64(successCount)/float64(len(testWorkOrders))*100)
	fmt.Println()

	fmt.Println("🔍 验证要点:")
	fmt.Println("   ✅ 确认每个工单都能触发API查询")
	fmt.Println("   ✅ 验证最简参数查询策略的稳定性")
	fmt.Println("   ✅ 检查不同工单的状态映射")
	fmt.Println("   ✅ 确认外部转发的一致性")
	fmt.Println("   ✅ 验证企业级错误处理")
	fmt.Println()
	
	fmt.Println("📋 请检查主系统日志确认:")
	fmt.Println("   🔥 每个工单都显示'🔥 使用最简参数查询工单'")
	fmt.Println("   🎯 每个工单都显示'🎯 最简参数查询成功找到工单！'或相应错误")
	fmt.Println("   📊 每个工单都有完整的API响应日志")
	fmt.Println("   🔄 每个工单都有状态映射和外部转发记录")
}

func testWorkOrderCallback(wo TestWorkOrder) bool {
	// 构造快递鸟工单回调数据
	callbackData := fmt.Sprintf(`{
		"PushTime": "2025-08-25 14:00:00",
		"EBusinessID": "1778716",
		"Data": [{
			"ShipperCode": "STO",
			"CreateTime": "2025-08-25 14:00:00",
			"OrderCode": "%s",
			"EBusinessID": "1778716",
			"PickerInfo": [{"PickupCode": "123456"}],
			"Cost": 10.00,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 5.00,
			"Reason": "测试工单回调 - %s",
			"TicketSource": 4,
			"LogisticCode": "%s",
			"TicketId": "TEST_%s",
			"State": "401",
			"FetchTime": "",
			"KDNOrderCode": "%s",
			"OperateType": 2
		}],
		"Count": 1
	}`, wo.OrderCode, wo.Description, wo.TrackingNo, wo.KDNOrderCode, wo.KDNOrderCode)

	// URL编码
	encodedData := url.QueryEscape(callbackData)
	
	// 构造请求体
	requestBody := fmt.Sprintf("RequestData=%s&DataSign=dGVzdF9zaWduYXR1cmU=&RequestType=103", encodedData)

	// 发送HTTP请求
	resp, err := http.Post(
		"http://localhost:8081/api/v1/callbacks/kuaidiniao",
		"application/x-www-form-urlencoded",
		bytes.NewBufferString(requestBody),
	)
	if err != nil {
		fmt.Printf("   ❌ HTTP请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ 读取响应失败: %v\n", err)
		return false
	}

	fmt.Printf("   📊 HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   📝 响应内容: %s\n", string(responseBody))

	return resp.StatusCode == 200
}
