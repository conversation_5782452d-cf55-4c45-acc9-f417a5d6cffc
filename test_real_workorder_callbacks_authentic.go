package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 真实工单回调测试数据
type AuthenticWorkOrderCallbackTest struct {
	Name        string
	Provider    string
	RawBody     string
	Description string
}

func main() {
	fmt.Println("=== 真实工单回调测试（使用数据库中的真实数据）===")
	fmt.Println("🔥 使用从数据库提取的真实工单回调数据")
	fmt.Println("📋 验证工单回复记录保存功能")
	fmt.Println("🎯 这次使用的是真正从数据库中提取的真实工单回调数据！")
	fmt.Println()

	// 真实的快递鸟工单回调数据 (从数据库提取的真实数据)
	kuaidiniaoRealTests := []AuthenticWorkOrderCallbackTest{
		{
			Name:        "快递鸟真实工单回调1 - 客服回复",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+11%3A09%3A39%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-25+14%3A35%3A36%22%2C%22OrderCode%22%3A%22GK20250824000001071%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PickupCode%22%3A%22908585%22%7D%5D%2C%22Cost%22%3A5.64%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A1.00%2C%22Reason%22%3A%22%E6%82%A8%E5%A5%BD%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B7%B2%E6%8E%A5%E6%94%B6%E5%88%B0%E6%82%A8%E7%9A%84%E5%8F%8D%E9%A6%88%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B7%B2%E5%9C%A8%E8%B7%9F%E8%BF%9B%E5%A4%84%E7%90%86%E4%B8%AD%EF%BC%8C%E8%AF%B7%E6%82%A8%E8%80%90%E5%BF%83%E7%AD%89%E5%BE%85%E3%80%82%22%2C%22TicketSource%22%3A4%2C%22LogisticCode%22%3A%22773373581523516%22%2C%22TicketId%22%3A%22CPN2508251410001251%22%2C%22State%22%3A%22401%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508241110000426%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=NzgxZjU4N2E0NTRiNWVhYjdlNDA5ZjU1ZWVlNTg1NGQ%3D&RequestType=103",
			Description: "工单ID: CPN2508251410001251, 订单号: GK20250824000001071, 回复: 您好，我们已接收到您的反馈，我们已在跟进处理中，请您耐心等待。",
		},
		{
			Name:        "快递鸟真实工单回调2 - 核实中回复",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+10%3A56%3A59%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-25+13%3A53%3A30%22%2C%22OrderCode%22%3A%22GK20250824000001002%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PickupCode%22%3A%22199104%22%7D%5D%2C%22Cost%22%3A48.54%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A25.00%2C%22Reason%22%3A%22%E5%B7%B2%E6%94%B6%E5%88%B0%E6%82%A8%E7%9A%84%E5%8F%8D%E9%A6%88%EF%BC%8C%E6%AD%A3%E5%9C%A8%E4%B8%8E%E5%BF%AB%E9%80%92%E5%85%AC%E5%8F%B8%E6%A0%B8%E5%AE%9E%E4%B8%AD%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B0%86%E5%9C%A824%E5%B0%8F%E6%97%B6%E5%86%85%E6%A0%B8%E5%AE%9E%E5%9B%9E%E5%A4%8D%E7%BB%93%E6%9E%9C%EF%BC%8C%E8%BE%9B%E8%8B%A6%E8%80%90%E5%BF%83%E7%AD%89%E5%BE%85%E4%B8%80%E4%B8%8B%7E%7E%22%2C%22TicketSource%22%3A4%2C%22LogisticCode%22%3A%22773373579876362%22%2C%22TicketId%22%3A%22CPN2508251310001419%22%2C%22State%22%3A%22401%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508241010002707%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=NGQwMjQ3NjExYzhlMmQ2NmQxY2MyNDc3ODM2ZjFiZmE%3D&RequestType=103",
			Description: "工单ID: CPN2508251310001419, 订单号: GK20250824000001002, 回复: 已收到您的反馈，正在与快递公司核实中，我们将在24小时内核实回复结果，辛苦耐心等待一下~~",
		},
		{
			Name:        "快递鸟真实工单回调3 - 核实处理中",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+15%3A19%3A45%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-25+13%3A53%3A30%22%2C%22OrderCode%22%3A%22GK20250824000002769%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PickupCode%22%3A%22220973%22%7D%5D%2C%22Cost%22%3A38.49%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A20.00%2C%22Reason%22%3A%22%E5%B7%B2%E6%94%B6%E5%88%B0%E6%82%A8%E7%9A%84%E5%8F%8D%E9%A6%88%EF%BC%8C%E6%AD%A3%E5%9C%A8%E4%B8%8E%E5%BF%AB%E9%80%92%E5%85%AC%E5%8F%B8%E6%A0%B8%E5%AE%9E%E4%B8%AD%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B0%86%E5%9C%A824%E5%B0%8F%E6%97%B6%E5%86%85%E6%A0%B8%E5%AE%9E%E5%9B%9E%E5%A4%8D%E7%BB%93%E6%9E%9C%EF%BC%8C%E8%BE%9B%E8%8B%A6%E8%80%90%E5%BF%83%E7%AD%89%E5%BE%85%E4%B8%80%E4%B8%8B%7E%7E%22%2C%22TicketSource%22%3A4%2C%22LogisticCode%22%3A%22773373636316658%22%2C%22TicketId%22%3A%22CPN2508251310002029%22%2C%22State%22%3A%22401%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508241510000912%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=ZTYxZmUzYTFmZDAwYjYwYTBmMTI0MjQyNmY1NTU0ZjU%3D&RequestType=103",
			Description: "工单ID: CPN2508251310002029, 订单号: GK20250824000002769, 回复: 已收到您的反馈，正在与快递公司核实中，我们将在24小时内核实回复结果，辛苦耐心等待一下~~",
		},
	}

	// 真实的快递100工单回调数据 (从数据库提取的真实数据)
	kuaidi100RealTests := []AuthenticWorkOrderCallbackTest{
		{
			Name:        "快递100真实工单回调1 - 工单状态更新",
			Provider:    "kuaidi100",
			RawBody:     `{"workorderId":5638536,"status":3,"callbackUrl":"http://************:8082/webhook/kuaidi100"}`,
			Description: "工单ID: 5638536, 状态: 3 (已完结)",
		},
		{
			Name:        "快递100真实工单回调2 - 包裹已取件",
			Provider:    "kuaidi100",
			RawBody:     `{"workorderId":5638536,"committer":null,"content":"包裹已取件并发出。","lastModified":1756102920426,"attach":null,"callbackUrl":null}`,
			Description: "工单ID: 5638536, 回复: 包裹已取件并发出。",
		},
	}

	fmt.Println("🔍 准备测试 5 个真实工单回调:")
	fmt.Println("   📦 快递鸟真实工单回调: 3个 (包含真实的TicketId、客服回复等)")
	fmt.Println("   📦 快递100真实工单回调: 2个 (包含真实的workorderId、状态更新等)")
	fmt.Println()

	// 测试快递鸟真实工单回调
	fmt.Println("🚀 开始测试快递鸟真实工单回调...")
	for i, test := range kuaidiniaoRealTests {
		fmt.Printf("\n🔥 测试 %d/3: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		
		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		
		if i < len(kuaidiniaoRealTests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")

	// 测试快递100真实工单回调
	fmt.Println("🚀 开始测试快递100真实工单回调...")
	for i, test := range kuaidi100RealTests {
		fmt.Printf("\n🔥 测试 %d/2: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		
		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		
		if i < len(kuaidi100RealTests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Println("=== 真实工单回调测试完成 ===")
	fmt.Println("📋 请检查主系统日志确认:")
	fmt.Println("   🔥 每个真实工单回调都能正确解析和处理")
	fmt.Println("   🎯 每个工单都能找到对应的工单记录")
	fmt.Println("   📊 每个工单都有完整的回复记录保存")
	fmt.Println("   🔄 每个工单都有状态映射和外部转发记录")
	fmt.Println()
	fmt.Println("🎉 这次测试使用的是真实的工单回调数据:")
	fmt.Println("   📦 快递鸟: 真实的TicketId、客服回复内容")
	fmt.Println("   📦 快递100: 真实的workorderId、状态更新")
	fmt.Println("   🔥 所有数据都是从数据库中提取的真实工单回调！")
}

// sendCallback 发送回调请求
func sendCallback(provider, rawBody string) bool {
	// 构建URL
	url := fmt.Sprintf("http://localhost:8081/api/v1/callbacks/%s", provider)
	
	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(rawBody))
	if err != nil {
		fmt.Printf("   ❌ 创建请求失败: %v\n", err)
		return false
	}
	
	// 设置请求头
	if provider == "kuaidi100" {
		req.Header.Set("Content-Type", "application/json")
	} else {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   ❌ 发送请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ 读取响应失败: %v\n", err)
		return false
	}
	
	fmt.Printf("   📊 HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   📝 响应内容: %s\n", string(body))
	
	return resp.StatusCode == 200
}
