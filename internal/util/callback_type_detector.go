package util

import "strings"

// DetermineCallbackType 智能判断回调类型
// 🔥 基于回调内容的关键字段判断是物流回调还是工单回调
func DetermineCallbackType(rawData []byte, headers map[string]string, provider string) string {
	// 转换为字符串进行内容分析
	content := strings.ToLower(string(rawData))

	// 🔥 供应商特定的工单回调识别
	switch provider {
	case "kuaidiniao":
		// 🔥 快递鸟工单回调特征：根据官方文档5.18，工单回调必须满足以下条件：
		// 1. RequestType=103 (固定值)
		// 2. State=401 (工单状态码，固定值)
		// 3. 包含工单特有字段：TicketId, TicketSource
		if (strings.Contains(content, "requesttype=103") ||
			strings.Contains(content, "\"requesttype\":\"103\"") ||
			strings.Contains(content, "\"requesttype\": \"103\"") ||
			strings.Contains(content, "\"requesttype\":103") ||
			strings.Contains(content, "\"requesttype\": 103")) &&
			// 🔥 关键修复：工单回调必须包含State=401（官方文档明确规定）
			// 支持URL编码和解码后的格式
			(strings.Contains(content, "\"state\":\"401\"") ||
				strings.Contains(content, "\"state\": \"401\"") ||
				strings.Contains(content, "state=401") ||
				strings.Contains(content, "\"State\":\"401\"") ||
				strings.Contains(content, "\"State\": \"401\"") ||
				strings.Contains(content, "state%22%3a%22401")) &&
			// 🔥 关键修复：工单回调必须包含TicketId或TicketSource字段
			(strings.Contains(content, "ticketid") ||
				strings.Contains(content, "ticketsource")) {
			return "workorder"
		}
		// 🔥 明确排除：所有非401状态的回调都是物流回调
		if (strings.Contains(content, "requesttype=103") ||
			strings.Contains(content, "\"requesttype\":\"103\"") ||
			strings.Contains(content, "\"requesttype\": \"103\"") ||
			strings.Contains(content, "\"requesttype\":103") ||
			strings.Contains(content, "\"requesttype\": 103")) &&
			!strings.Contains(content, "\"state\":\"401\"") &&
			!strings.Contains(content, "\"state\": \"401\"") &&
			!strings.Contains(content, "state=401") &&
			!strings.Contains(content, "\"State\":\"401\"") &&
			!strings.Contains(content, "\"State\": \"401\"") &&
			!strings.Contains(content, "state%22%3a%22401") {
			return "logistics" // 这是订单状态回调，不是工单回调
		}
	case "yuntong":
		// 云通工单回调特征：RequestType=105，包含ComplaintNumber、ComplaintType
		if strings.Contains(content, "complaintnumber") ||
			strings.Contains(content, "complainttype") ||
			strings.Contains(content, "\"requesttype\":105") ||
			strings.Contains(content, "\"requesttype\": 105") {
			return "workorder"
		}
	case "kuaidi100":
		// 快递100工单回调特征：包含工单ID、状态等
		if strings.Contains(content, "consultid") ||
			strings.Contains(content, "secondtype") ||
			strings.Contains(content, "workorder") {
			return "workorder"
		}
	case "yida":
		// 易达工单回调特征：包含taskNo、工单类型等
		if strings.Contains(content, "taskno") ||
			strings.Contains(content, "task_no") ||
			strings.Contains(content, "workordertype") {
			return "workorder"
		}
	}

	// 🔥 通用工单关键词检测
	workorderKeywords := []string{
		"workorder", "ticket", "complaint", "issue", "problem",
		"ticketid", "ticketsource", "complaintnumber", "complainttype",
		"consultid", "taskno", "task_no", "workordertype",
	}

	// 🔥 通用物流关键词检测
	logisticsKeywords := []string{
		"tracking", "shipment", "delivery", "logistics", "express",
		"logisticcode", "trackingno", "waybill", "package", "courier",
		"pickup", "transit", "delivered", "exception", "status",
	}

	// 统计工单关键词出现次数
	workorderScore := 0
	for _, keyword := range workorderKeywords {
		if strings.Contains(content, strings.ToLower(keyword)) {
			workorderScore++
		}
	}

	// 统计物流关键词出现次数
	logisticsScore := 0
	for _, keyword := range logisticsKeywords {
		if strings.Contains(content, strings.ToLower(keyword)) {
			logisticsScore++
		}
	}

	// 🔥 基于评分决定回调类型
	if workorderScore > logisticsScore && workorderScore > 0 {
		return "workorder"
	} else if logisticsScore > 0 {
		return "logistics"
	}

	// 🔥 基于URL路径判断（向后兼容）
	if strings.Contains(content, "workorder") || strings.Contains(content, "ticket") {
		return "workorder"
	}

	// 默认为物流回调
	return "logistics"
}
