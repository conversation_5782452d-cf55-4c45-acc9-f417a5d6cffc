package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// 工单ID搜索测试
type WorkOrderIDSearchTest struct {
	Name        string
	SearchTerm  string
	Description string
}

func main() {
	fmt.Println("=== 工单ID搜索功能测试 ===")
	fmt.Println("🔥 测试平台工单ID和供应商工单ID搜索功能")
	fmt.Println("📋 验证精确匹配和模糊匹配")
	fmt.Println()

	// 测试用例
	searchTests := []WorkOrderIDSearchTest{
		{
			Name:        "平台工单ID精确匹配",
			SearchTerm:  "e8b30f23-143f-4a98-b698-e5d0a3b99c4a",
			Description: "使用完整的平台工单ID进行精确搜索",
		},
		{
			Name:        "平台工单ID部分匹配",
			SearchTerm:  "e8b30f23",
			Description: "使用平台工单ID的前8位进行模糊搜索",
		},
		{
			Name:        "快递鸟工单ID精确匹配",
			SearchTerm:  "CPN2508251310002029",
			Description: "使用完整的快递鸟工单ID进行精确搜索",
		},
		{
			Name:        "快递鸟工单ID部分匹配",
			SearchTerm:  "CPN25082513",
			Description: "使用快递鸟工单ID的前10位进行模糊搜索",
		},
		{
			Name:        "快递100工单ID精确匹配",
			SearchTerm:  "5638536",
			Description: "使用完整的快递100工单ID进行精确搜索",
		},
		{
			Name:        "快递100工单ID部分匹配",
			SearchTerm:  "563853",
			Description: "使用快递100工单ID的前6位进行模糊搜索",
		},
		{
			Name:        "不存在的工单ID",
			SearchTerm:  "NONEXISTENT123",
			Description: "搜索不存在的工单ID，验证空结果处理",
		},
	}

	fmt.Printf("🔍 准备测试 %d 个工单ID搜索场景:\n", len(searchTests))
	for i, test := range searchTests {
		fmt.Printf("   %d. %s\n", i+1, test.Name)
	}
	fmt.Println()

	// 执行测试
	for i, test := range searchTests {
		fmt.Printf("🔥 测试 %d/%d: %s\n", i+1, len(searchTests), test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		fmt.Printf("   🔍 搜索词: %s\n", test.SearchTerm)

		success, resultCount := testWorkOrderIDSearch(test.SearchTerm)
		if success {
			fmt.Printf("   ✅ 测试成功 - 找到 %d 个工单\n", resultCount)
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}

		if i < len(searchTests)-1 {
			fmt.Printf("\n⏳ 等待1秒...\n")
			time.Sleep(1 * time.Second)
		}
		fmt.Println()
	}

	fmt.Println("=== 工单ID搜索功能测试完成 ===")
	fmt.Println("📋 测试总结:")
	fmt.Println("   🔥 验证了平台工单ID和供应商工单ID的搜索功能")
	fmt.Println("   🎯 测试了精确匹配和模糊匹配")
	fmt.Println("   📊 验证了搜索结果的正确性")
	fmt.Println("   🔄 测试了空结果的处理")
}

// testWorkOrderIDSearch 测试工单ID搜索
func testWorkOrderIDSearch(workOrderID string) (bool, int) {
	// 构建查询参数
	params := url.Values{}
	params.Set("work_order_id", workOrderID)
	params.Set("page", "1")
	params.Set("page_size", "20")

	// 构建URL
	baseURL := "http://localhost:8081/api/v1/workorders"
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		fmt.Printf("   ❌ 创建请求失败: %v\n", err)
		return false, 0
	}

	// 设置请求头（使用真实的用户认证token）
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnby1rdWFpZGktcHJvZHVjdGlvbiIsInN1YiI6ImQ3ZTQ1ZmY0LWNiM2QtNDcwYy05ZmJjLTIyMTE0NjM5ZDA5NiIsImF1ZCI6WyJnby1rdWFpZGktYXBpLXByb2R1Y3Rpb24iXSwiZXhwIjoxNzU4Njk3MTIxLCJuYmYiOjE3NTYxMDQ4MjEsImlhdCI6MTc1NjEwNDgyMSwianRpIjoiMDlmNGVjYWItNTg2OC00M2Y0LTg2NWEtM2I3MGFiOTI5ODIxIiwiY2xpZW50X2lkIjoiZDdlNDVmZjQtY2IzZC00NzBjLTlmYmMtMjIxMTQ2MzlkMDk2Iiwic2NvcGVzIjpbImV4cHJlc3M6cmVhZCIsImV4cHJlc3M6d3JpdGUiLCJ3b3Jrb3JkZXI6cmVhZCIsIndvcmtvcmRlcjp3cml0ZSJdfQ.Z1E6Walh87fvqouSkmuUcQC9WkmQ7J9CqR4Faw0KQT6vqFbXMhqEU_oTGBE3-J7wYdP_zqVj0Uf8oJ3KKoNP1EFUEM3PY-qGspDMvVnd1Z2YMgFWkYb7Xyrc4woUkK5UekVt5iYJsEpGGmO4uakStEPCis5l1Zxkk17rHVfXmpN37XYekSGWew6zDEE6YlRb9C2bDptOVnkOYd7gyPn_SIzLzWrg-QtGwe4H-8D6T9hO07bA7lyZaJZLE4R_CBt9hgJ-scRx5LbInWZmpr1koRxGe9IWXzveWUnR6jPO40XjUvJE6nrzNmf_FxAX5jfVvJiRzvayL-lwR_pvgsWfzQ")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   ❌ 发送请求失败: %v\n", err)
		return false, 0
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ 读取响应失败: %v\n", err)
		return false, 0
	}

	fmt.Printf("   📊 HTTP状态码: %d\n", resp.StatusCode)

	if resp.StatusCode != 200 {
		fmt.Printf("   📝 错误响应: %s\n", string(body))
		return false, 0
	}

	// 解析响应
	var response struct {
		Success bool `json:"success"`
		Data    struct {
			Items []map[string]interface{} `json:"items"`
			Total int64                    `json:"total"`
		} `json:"data"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Printf("   ❌ 解析响应失败: %v\n", err)
		return false, 0
	}

	if !response.Success {
		fmt.Printf("   ❌ API返回失败: %s\n", response.Message)
		return false, 0
	}

	resultCount := len(response.Data.Items)
	fmt.Printf("   📊 搜索结果: 总数=%d, 当前页=%d\n", response.Data.Total, resultCount)

	// 显示搜索结果详情
	if resultCount > 0 {
		fmt.Printf("   📋 搜索结果详情:\n")
		for i, item := range response.Data.Items {
			if i >= 3 { // 只显示前3个结果
				fmt.Printf("      ... 还有 %d 个结果\n", resultCount-3)
				break
			}

			id := item["id"]
			providerWorkOrderID := item["provider_work_order_id"]
			title := item["title"]

			fmt.Printf("      %d. 平台ID: %v\n", i+1, id)
			if providerWorkOrderID != nil {
				fmt.Printf("         供应商ID: %v\n", providerWorkOrderID)
			}
			fmt.Printf("         标题: %v\n", title)
		}
	} else {
		fmt.Printf("   📋 未找到匹配的工单\n")
	}

	return true, resultCount
}
