<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单ID搜索功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #6c757d;
        }
        .clear-btn:hover {
            background: #545b62;
        }
        .results-section {
            margin-top: 30px;
        }
        .result-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .work-order-id {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .highlight {
            background: #fff2cc;
            color: #e6a23c;
            font-weight: 500;
        }
        .id-label {
            color: #6c757d;
            font-size: 11px;
            margin-right: 5px;
        }
        .loading {
            text-align: center;
            color: #6c757d;
            padding: 20px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-buttons {
            margin-top: 15px;
        }
        .test-btn {
            background: #28a745;
            font-size: 12px;
            padding: 5px 10px;
            margin: 2px;
        }
        .test-btn:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 工单ID搜索功能测试</h1>
        
        <div class="search-section">
            <div class="form-group">
                <label for="workOrderId">工单ID搜索</label>
                <input type="text" id="workOrderId" placeholder="请输入工单ID（平台工单ID或供应商工单ID）">
            </div>
            
            <div class="form-group">
                <button onclick="searchWorkOrders()">🔍 搜索工单</button>
                <button class="clear-btn" onclick="clearSearch()">🗑️ 清空</button>
            </div>
            
            <div class="test-buttons">
                <strong>快速测试：</strong><br>
                <button class="test-btn" onclick="testSearch('CPN2508251410001251')">快递鸟工单ID</button>
                <button class="test-btn" onclick="testSearch('e8b30f23-143f-4a98-b698-e5d0a3b99c4a')">平台工单ID</button>
                <button class="test-btn" onclick="testSearch('5641407')">快递100工单ID</button>
                <button class="test-btn" onclick="testSearch('e8b30f23')">部分匹配</button>
                <button class="test-btn" onclick="testSearch('NONEXISTENT')">不存在的ID</button>
            </div>
        </div>

        <div id="results" class="results-section"></div>
    </div>

    <script>
        // 用户token（需要先登录获取）
        const userToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnby1rdWFpZGktcHJvZHVjdGlvbiIsInN1YiI6ImQ3ZTQ1ZmY0LWNiM2QtNDcwYy05ZmJjLTIyMTE0NjM5ZDA5NiIsImF1ZCI6WyJnby1rdWFpZGktYXBpLXByb2R1Y3Rpb24iXSwiZXhwIjoxNzU4Njk3MTIxLCJuYmYiOjE3NTYxMDQ4MjEsImlhdCI6MTc1NjEwNDgyMSwianRpIjoiMDlmNGVjYWItNTg2OC00M2Y0LTg2NWEtM2I3MGFiOTI5ODIxIiwiY2xpZW50X2lkIjoiZDdlNDVmZjQtY2IzZC00NzBjLTlmYmMtMjIxMTQ2MzlkMDk2Iiwic2NvcGVzIjpbImV4cHJlc3M6cmVhZCIsImV4cHJlc3M6d3JpdGUiLCJ3b3Jrb3JkZXI6cmVhZCIsIndvcmtvcmRlcjp3cml0ZSJdfQ.Z1E6Walh87fvqouSkmuUcQC9WkmQ7J9CqR4Faw0KQT6vqFbXMhqEU_oTGBE3-J7wYdP_zqVj0Uf8oJ3KKoNP1EFUEM3PY-qGspDMvVnd1Z2YMgFWkYb7Xyrc4woUkK5UekVt5iYJsEpGGmO4uakStEPCis5l1Zxkk17rHVfXmpN37XYekSGWew6zDEE6YlRb9C2bDptOVnkOYd7gyPn_SIzLzWrg-QtGwe4H-8D6T9hO07bA7lyZaJZLE4R_CBt9hgJ-scRx5LbInWZmpr1koRxGe9IWXzveWUnR6jPO40XjUvJE6nrzNmf_FxAX5jfVvJiRzvayL-lwR_pvgsWfzQ';

        async function searchWorkOrders() {
            const workOrderId = document.getElementById('workOrderId').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!workOrderId) {
                showError('请输入工单ID');
                return;
            }

            showLoading();

            try {
                const params = new URLSearchParams({
                    work_order_id: workOrderId,
                    page: 1,
                    page_size: 10
                });

                const response = await fetch(`http://localhost:8081/api/v1/workorders?${params}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${userToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success && data.data) {
                    displayResults(data.data, workOrderId);
                } else {
                    showError(data.message || '搜索失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        function displayResults(data, searchTerm) {
            const resultsDiv = document.getElementById('results');
            const items = data.items || [];
            
            if (items.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="success">
                        <h3>搜索完成</h3>
                        <p>未找到匹配的工单，搜索词: "${searchTerm}"</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="success">
                    <h3>🎉 搜索成功</h3>
                    <p>找到 ${data.total} 个工单，当前显示前 ${items.length} 个结果</p>
                    <p>搜索词: "${searchTerm}"</p>
                </div>
            `;

            items.forEach((item, index) => {
                const platformIdMatch = item.id.toLowerCase().includes(searchTerm.toLowerCase());
                const providerIdMatch = item.provider_work_order_id && 
                    item.provider_work_order_id.toLowerCase().includes(searchTerm.toLowerCase());

                html += `
                    <div class="result-item">
                        <h4>${item.title}</h4>
                        <div style="margin: 10px 0;">
                            <div>
                                <span class="id-label">平台工单ID:</span>
                                <span class="work-order-id ${platformIdMatch ? 'highlight' : ''}">${item.id}</span>
                            </div>
                            ${item.provider_work_order_id ? `
                                <div style="margin-top: 5px;">
                                    <span class="id-label">供应商工单ID:</span>
                                    <span class="work-order-id ${providerIdMatch ? 'highlight' : ''}">${item.provider_work_order_id}</span>
                                </div>
                            ` : ''}
                        </div>
                        <p><strong>订单号:</strong> ${item.order_no}</p>
                        <p><strong>运单号:</strong> ${item.tracking_no}</p>
                        <p><strong>供应商:</strong> ${item.provider}</p>
                        <p><strong>状态:</strong> ${item.status_name}</p>
                        <p><strong>类型:</strong> ${item.type_name}</p>
                        <p><strong>创建时间:</strong> ${new Date(item.created_at).toLocaleString('zh-CN')}</p>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        function showLoading() {
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <h3>🔍 搜索中...</h3>
                    <p>正在查找匹配的工单，请稍候...</p>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <h3>❌ 搜索失败</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        function clearSearch() {
            document.getElementById('workOrderId').value = '';
            document.getElementById('results').innerHTML = '';
        }

        function testSearch(workOrderId) {
            document.getElementById('workOrderId').value = workOrderId;
            searchWorkOrders();
        }

        // 回车键搜索
        document.getElementById('workOrderId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchWorkOrders();
            }
        });
    </script>
</body>
</html>
