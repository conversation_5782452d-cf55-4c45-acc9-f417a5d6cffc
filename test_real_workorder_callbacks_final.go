package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 真实工单回调测试数据
type RealWorkOrderCallbackTest struct {
	Name        string
	Provider    string
	RawBody     string
	Description string
}

func main() {
	fmt.Println("=== 真正的工单回调测试 ===")
	fmt.Println("🔥 使用从数据库提取的真实工单回调数据")
	fmt.Println("📋 验证工单回复记录保存功能")
	fmt.Println("🎯 这次是真正的工单回调，不是物流回调！")
	fmt.Println()

	// 真实的快递鸟工单回调数据 (包含TicketId等工单字段)
	kuaidiniaoWorkOrderTests := []RealWorkOrderCallbackTest{
		{
			Name:        "快递鸟工单回调1 - 工单处理完成",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+21%3A35%3A00%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-24+21%3A35%3A00%22%2C%22OrderCode%22%3A%22GK20250824000000003%22%2C%22EBusinessID%22%3A%221778716%22%2C%22TicketPic%22%3A%22http%3A//example.com/ticket.jpg%22%2C%22Cost%22%3A6.50%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A2.0%2C%22Reason%22%3A%22%E5%B7%A5%E5%8D%95%E5%A4%84%E7%90%86%E5%AE%8C%E6%88%90%EF%BC%8C%E9%97%AE%E9%A2%98%E5%B7%B2%E8%A7%A3%E5%86%B3%22%2C%22DealResultFiles%22%3A%22%22%2C%22TicketSource%22%3A4%2C%22LogisticCode%22%3A%22TEST123456791%22%2C%22TicketId%22%3A%22TK2508242135000001%22%2C%22State%22%3A%22401%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508242135000003%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=VGVzdFNpZ25hdHVyZUZvclRlc3Q%3D&RequestType=103",
			Description: "工单ID: TK2508242135000001, 订单号: GK20250824000000003, 状态: 工单处理完成，问题已解决",
		},
		{
			Name:        "快递鸟工单回调2 - 工单处理完成",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+21%3A25%3A00%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-24+21%3A25%3A00%22%2C%22OrderCode%22%3A%22GK20250824000000002%22%2C%22EBusinessID%22%3A%221778716%22%2C%22TicketPic%22%3A%22http%3A//example.com/ticket.jpg%22%2C%22Cost%22%3A5.50%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A1.50%2C%22Reason%22%3A%22%E5%B7%A5%E5%8D%95%E5%A4%84%E7%90%86%E5%AE%8C%E6%88%90%22%2C%22DealResultFiles%22%3A%22%22%2C%22TicketSource%22%3A4%2C%22LogisticCode%22%3A%22TEST123456790%22%2C%22TicketId%22%3A%22TK2508242125000001%22%2C%22State%22%3A%22401%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508242125000002%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=VGVzdFNpZ25hdHVyZUZvclRlc3Q%3D&RequestType=103",
			Description: "工单ID: TK2508242125000001, 订单号: GK20250824000000002, 状态: 工单处理完成",
		},
	}

	// 真实的快递100工单回调数据 (包含workorderId等工单字段)
	kuaidi100WorkOrderTests := []RealWorkOrderCallbackTest{
		{
			Name:        "快递100工单回调1 - 工单已完结",
			Provider:    "kuaidi100",
			RawBody:     `{"workorderId":5618531,"committer":null,"content":"您本次反馈的问题，已处理完毕，工单已完结","lastModified":1755784807061,"attach":null,"callbackUrl":null}`,
			Description: "工单ID: 5618531, 状态: 您本次反馈的问题，已处理完毕，工单已完结",
		},
		{
			Name:        "快递100工单回调2 - 工单已完结",
			Provider:    "kuaidi100",
			RawBody:     `{"workorderId":5601081,"committer":null,"content":"您本次反馈的问题，已处理完毕，工单已完结","lastModified":1755572406801,"attach":null,"callbackUrl":null}`,
			Description: "工单ID: 5601081, 状态: 您本次反馈的问题，已处理完毕，工单已完结",
		},
	}

	fmt.Println("🔍 准备测试 4 个真实工单回调:")
	fmt.Println("   📦 快递鸟工单回调: 2个 (包含TicketId、TicketSource等工单字段)")
	fmt.Println("   📦 快递100工单回调: 2个 (包含workorderId、content等工单字段)")
	fmt.Println()

	// 测试快递鸟工单回调
	fmt.Println("🚀 开始测试快递鸟工单回调...")
	for i, test := range kuaidiniaoWorkOrderTests {
		fmt.Printf("\n🔥 测试 %d/2: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		
		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		
		if i < len(kuaidiniaoWorkOrderTests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")

	// 测试快递100工单回调
	fmt.Println("🚀 开始测试快递100工单回调...")
	for i, test := range kuaidi100WorkOrderTests {
		fmt.Printf("\n🔥 测试 %d/2: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		
		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		
		if i < len(kuaidi100WorkOrderTests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Println("=== 真正的工单回调测试完成 ===")
	fmt.Println("📋 请检查主系统日志确认:")
	fmt.Println("   🔥 每个工单回调都能正确解析和处理")
	fmt.Println("   🎯 每个工单都能找到对应的工单记录")
	fmt.Println("   📊 每个工单都有完整的回复记录保存")
	fmt.Println("   🔄 每个工单都有状态映射和外部转发记录")
	fmt.Println()
	fmt.Println("🎉 这次测试的是真正的工单回调数据:")
	fmt.Println("   📦 快递鸟: 包含TicketId、TicketSource、DealResultFiles等工单字段")
	fmt.Println("   📦 快递100: 包含workorderId、content、committer等工单字段")
}

// sendCallback 发送回调请求
func sendCallback(provider, rawBody string) bool {
	// 构建URL
	url := fmt.Sprintf("http://localhost:8081/api/v1/callbacks/%s", provider)
	
	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(rawBody))
	if err != nil {
		fmt.Printf("   ❌ 创建请求失败: %v\n", err)
		return false
	}
	
	// 设置请求头
	if provider == "kuaidi100" {
		req.Header.Set("Content-Type", "application/json")
	} else {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   ❌ 发送请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ 读取响应失败: %v\n", err)
		return false
	}
	
	fmt.Printf("   📊 HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   📝 响应内容: %s\n", string(body))
	
	return resp.StatusCode == 200
}
