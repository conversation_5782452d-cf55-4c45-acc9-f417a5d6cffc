package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 真实回调测试数据
type RealCallbackTest struct {
	Name        string
	Provider    string
	RawBody     string
	Description string
}

func main() {
	fmt.Println("=== 真实工单回调测试 ===")
	fmt.Println("🔥 使用从数据库提取的真实工单回调数据")
	fmt.Println("📋 验证工单回复记录保存功能")
	fmt.Println()

	// 真实的快递鸟工单回调数据 (从数据库提取)
	kuaidiniaoTests := []RealCallbackTest{
		{
			Name:        "快递鸟工单1 - 在途状态",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+11%3A17%3A30%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-25+14%3A32%3A14%22%2C%22OrderCode%22%3A%22GK20250824000001117%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PickupCode%22%3A%22025774%22%7D%5D%2C%22Cost%22%3A4.54%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A1.00%2C%22Reason%22%3A%22%E3%80%90%E5%8C%97%E4%BA%AC%E5%B8%82%E3%80%91%E5%BF%AB%E4%BB%B6%E5%B7%B2%E5%8F%91%E5%BE%80+%E5%8C%97%E4%BA%AC%E8%BD%AC%E8%BF%90%E4%B8%AD%E5%BF%83%22%2C%22LogisticCode%22%3A%22773373586105840%22%2C%22State%22%3A%222%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508241110000781%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=MjY4YmZiZjViNTFlODM2YmYzOGY5NTIxZTMxNTY5MjI%3D&RequestType=103",
			Description: "订单号: GK20250824000001117, 运单号: 773373586105840, 状态: 在途",
		},
		{
			Name:        "快递鸟工单2 - 已签收",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-21+08%3A05%3A47%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22LogisticCode%22%3A%22773373002245543%22%2C%22ShipperCode%22%3A%22STO%22%2C%22State%22%3A%223%22%2C%22CreateTime%22%3A%222025-08-25+14%3A29%3A41%22%2C%22KDNOrderCode%22%3A%22KDN2508210810000090%22%2C%22OrderCode%22%3A%22GK20250821000000264%22%2C%22Reason%22%3A%22%E3%80%90%E6%B4%BE%E4%BB%B6%E5%BC%82%E5%B8%B8%E5%90%8E%E6%9C%80%E7%BB%88%E7%AD%BE%E6%94%B6%E3%80%91%E5%B7%B2%E7%AD%BE%E6%94%B6%EF%BC%8C%E7%AD%BE%E6%94%B6%E4%BA%BA%E5%87%AD%E5%8F%96%E8%B4%A7%E7%A0%81%E7%AD%BE%E6%94%B6%E3%80%82%22%2C%22SignType%22%3A%223001%22%2C%22CallRequestType%22%3A%221801%22%7D%5D%2C%22Count%22%3A1%7D&DataSign=YTQzMTdmYWIwOTk5ZjI3NDVkOWZmZGY4ZWFiYjI5YTg%3D&RequestType=103",
			Description: "订单号: GK20250821000000264, 运单号: 773373002245543, 状态: 已签收",
		},
		{
			Name:        "快递鸟工单3 - 已揽件",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-24+18%3A30%3A26%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22ActualWeight%22%3A1.00%2C%22FirstWeightAmount%22%3A%224.29%22%2C%22Cost%22%3A4.29%2C%22Success%22%3Atrue%2C%22Reason%22%3A%22%E5%B7%B2%E6%8F%BD%E4%BB%B6%22%2C%22VolumeWeight%22%3A0.000%2C%22IsSubsectionContinuousWeightPrice%22%3A0%2C%22OperateType%22%3A2%2C%22ContinuousWeight%22%3A0%2C%22CreateTime%22%3A%222025-08-25+14%3A31%3A14%22%2C%22ContinuousWeightAmount%22%3A%220.00%22%2C%22OrderCode%22%3A%22GK20250824000004180%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PickupCode%22%3A%22007399%22%7D%5D%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A1.00%2C%22ContinuousWeightPrice%22%3A1.0000%2C%22LogisticCode%22%3A%22773373669879975%22%2C%22TotalFee%22%3A4.29%2C%22Volume%22%3A0.000%2C%22State%22%3A%22601%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508241810000959%22%2C%22FirstWeight%22%3A1.00%2C%22OtherFeeDetail%22%3A%22%7B%7D%22%2C%22OtherFee%22%3A%220.00%22%7D%5D%2C%22Count%22%3A1%7D&DataSign=YTg3YjZlMGQ1MzU3ZmYzOTM2NGQzMTc1Yjc1NjIzZTc%3D&RequestType=103",
			Description: "订单号: GK20250824000004180, 运单号: 773373669879975, 状态: 已揽件",
		},
	}

	// 真实的快递100工单回调数据 (从数据库提取)
	kuaidi100Tests := []RealCallbackTest{
		{
			Name:        "快递100工单1 - 已签收",
			Provider:    "kuaidi100",
			RawBody:     "param=%7B%22data%22%3A%7B%22orderId%22%3A%***********%22%2C%22courierMobile%22%3A%2219917773517%22%2C%22freight%22%3A%225.80%22%2C%22weight%22%3A%221.00%22%2C%22pollToken%22%3A%22t5t0f81Y%2BPz1HI%2F1OH7IgbBADtQvZWpW6slMLzlRYHQ%3D%22%2C%22updateTime%22%3A1756103564670%2C%22sentStatus%22%3A13%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E5%BC%A0%E6%B0%B8%E9%87%91%22%2C%22defPrice%22%3A%2215.00%22%2C%22trace%22%3A%22%E5%8C%85%E8%A3%B9%E5%B7%B2%E7%AD%BE%E6%94%B6%EF%BC%81%EF%BC%88%E5%87%AD%E5%8F%96%E4%BB%B6%E7%A0%81%EF%BC%89%E5%A6%82%E6%9C%89%E9%97%AE%E9%A2%98%E8%AF%B7%E7%94%B5%E8%81%94%EF%BC%9A%E4%BB%A3%E6%94%B6%E7%82%B9+13028778545%2FLS%E9%A9%AC%E7%A6%8F%E6%89%8D%28%E6%A2%81%E5%AE%B6%E5%AF%BA%29+13028778545%EF%BC%8C%E6%8A%95%E8%AF%89%E7%94%B5%E8%AF%9D%EF%BC%9A0930-7184432%22%2C%22thirdOrderId%22%3A%22GK20250819000003771%22%2C%22payStatus%22%3A0%2C%22status%22%3A13%7D%2C%22kuaidicom%22%3A%22jtexpress%22%2C%22kuaidinum%22%3A%22JT0018321418797%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=DDC2AD9F94CF88333CB056B6747758D0&taskId=5860F11F6BBADE66F69A07CD197B79A3",
			Description: "工单ID: 301674406, 订单号: GK20250819000003771, 运单号: JT0018321418797, 状态: 已签收",
		},
		{
			Name:        "快递100工单2 - 运输中",
			Provider:    "kuaidi100",
			RawBody:     "param=%7B%22data%22%3A%7B%22orderId%22%3A%22303057340%22%2C%22courierMobile%22%3A%2218631138640%22%2C%22freight%22%3A%224.29%22%2C%22weight%22%3A%221.00%22%2C%22pollToken%22%3A%22c2HIHcNWSP73iWMw9TEIQns0OSQMHhCgtaZfOLQYDXk%3D%22%2C%22updateTime%22%3A1756103558277%2C%22sentStatus%22%3A101%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E5%B0%9A%E5%BD%A6%E6%95%8F%22%2C%22defPrice%22%3A%2210.00%22%2C%22trace%22%3A%22%E5%BF%AB%E4%BB%B6%E7%A6%BB%E5%BC%80%E3%80%90%E7%9F%B3%E5%AE%B6%E5%BA%84%E7%81%B5%E5%AF%BF%E5%8E%BF%E7%BD%91%E7%82%B9%E3%80%91%E5%B7%B2%E5%8F%91%E5%BE%80%E3%80%90%E7%9F%B3%E5%AE%B6%E5%BA%84%E8%BD%AC%E8%BF%90%E4%B8%AD%E5%BF%83%E3%80%91%EF%BC%88%E7%89%A9%E6%B5%81%E9%97%AE%E9%A2%98%E6%97%A0%E9%9C%80%E6%89%BE%E5%95%86%E5%AE%B6%2F%E5%B9%B3%E5%8F%B0%EF%BC%8C%E8%AF%B7%E8%81%94%E7%B3%BB956025%E4%B8%BA%E6%82%A8%E8%A7%A3%E5%86%B3%EF%BC%89%22%2C%22thirdOrderId%22%3A%22GK20250824000003583%22%2C%22payStatus%22%3A0%2C%22status%22%3A101%7D%2C%22kuaidicom%22%3A%22jtexpress%22%2C%22kuaidinum%22%3A%22JT0018418461767%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=DFDBF63A3113B0B2C14C03448606DF7E&taskId=025FCCCFFDE5F88CDBF4EC8F60223C96",
			Description: "工单ID: 303057340, 订单号: GK20250824000003583, 运单号: JT0018418461767, 状态: 运输中",
		},
		{
			Name:        "快递100工单3 - 已签收",
			Provider:    "kuaidi100",
			RawBody:     "param=%7B%22data%22%3A%7B%22orderId%22%3A%22301608892%22%2C%22courierMobile%22%3A%2218867364272%22%2C%22freight%22%3A%2222.20%22%2C%22weight%22%3A%227.00%22%2C%22pollToken%22%3A%222y2A9kAIVY63gQYiUCqCpSV2YETB2nLTjCSaMKA0VCY%3D%22%2C%22updateTime%22%3A1756103555130%2C%22sentStatus%22%3A13%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E9%83%AD%E9%98%B3%22%2C%22defPrice%22%3A%2266.00%22%2C%22trace%22%3A%22%E5%8C%85%E8%A3%B9%E5%B7%B2%E7%AD%BE%E6%94%B6%EF%BC%81%EF%BC%88%E5%87%AD%E5%8F%96%E4%BB%B6%E7%A0%81%EF%BC%89%E5%A6%82%E6%9C%89%E9%97%AE%E9%A2%98%E8%AF%B7%E7%94%B5%E8%81%94%EF%BC%9A%E4%BB%BB%E6%9C%AF%E5%B3%B0+13054279121%EF%BC%8C%E6%8A%95%E8%AF%89%E7%94%B5%E8%AF%9D%EF%BC%9A0451-81640061%22%2C%22thirdOrderId%22%3A%22GK20250819000002609%22%2C%22payStatus%22%3A0%2C%22status%22%3A13%7D%2C%22kuaidicom%22%3A%22jtexpress%22%2C%22kuaidinum%22%3A%22JT0018311883345%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=E8C5EF59D860D050301CEF6E5F392F53&taskId=2D6939901348F15342197905EE329DB9",
			Description: "工单ID: 301608892, 订单号: GK20250819000002609, 运单号: JT0018311883345, 状态: 已签收",
		},
	}

	fmt.Println("🔍 准备测试 6 个真实工单回调:")
	fmt.Println("   📦 快递鸟工单: 3个")
	fmt.Println("   📦 快递100工单: 3个")
	fmt.Println()

	// 测试快递鸟工单回调
	fmt.Println("🚀 开始测试快递鸟工单回调...")
	for i, test := range kuaidiniaoTests {
		fmt.Printf("\n🔥 测试 %d/3: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		
		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		
		if i < len(kuaidiniaoTests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")

	// 测试快递100工单回调
	fmt.Println("🚀 开始测试快递100工单回调...")
	for i, test := range kuaidi100Tests {
		fmt.Printf("\n🔥 测试 %d/3: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)
		
		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}
		
		if i < len(kuaidi100Tests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Println("=== 真实工单回调测试完成 ===")
	fmt.Println("📋 请检查主系统日志确认:")
	fmt.Println("   🔥 每个工单都能正确解析和处理")
	fmt.Println("   🎯 每个工单都能找到对应的工单记录")
	fmt.Println("   📊 每个工单都有完整的回复记录保存")
	fmt.Println("   🔄 每个工单都有状态映射和外部转发记录")
}

// sendCallback 发送回调请求
func sendCallback(provider, rawBody string) bool {
	// 构建URL
	url := fmt.Sprintf("http://localhost:8081/api/v1/callbacks/%s", provider)
	
	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(rawBody))
	if err != nil {
		fmt.Printf("   ❌ 创建请求失败: %v\n", err)
		return false
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   ❌ 发送请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ 读取响应失败: %v\n", err)
		return false
	}
	
	fmt.Printf("   📊 HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   📝 响应内容: %s\n", string(body))
	
	return resp.StatusCode == 200
}
