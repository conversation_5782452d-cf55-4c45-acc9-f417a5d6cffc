package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("=== 真实快递鸟工单回调完整方案验证 ===")
	fmt.Println("🔥 使用数据库中的真实工单回调数据")
	fmt.Println("📋 验证完整的API查询 + 状态映射 + 外部转发链路")
	fmt.Println()

	// 🔥 从数据库获取的真实快递鸟工单回调数据
	realKuaidiniaoWorkorderCallback := `RequestData=%7B%22PushTime%22%3A%222025-08-17+18%3A52%3A57%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-08-24+21%3A35%3A28%22%2C%22OrderCode%22%3A%22GK20250817000004210%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PickupCode%22%3A%22484987%22%7D%5D%2C%22Cost%22%3A17.79%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A10.00%2C%22Reason%22%3A%22%E6%82%A8%E5%A5%BD%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B7%B2%E6%8E%A5%E6%94%B6%E5%88%B0%E6%82%A8%E7%9A%84%E5%8F%8D%E9%A6%88%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B7%B2%E5%9C%A8%E8%B7%9F%E8%BF%9B%E5%A4%84%E7%90%86%E4%B8%AD%EF%BC%8C%E8%AF%B7%E6%82%A8%E8%80%90%E5%BF%83%E7%AD%89%E5%BE%85%E3%80%82%22%2C%22TicketSource%22%3A4%2C%22LogisticCode%22%3A%22773372416148735%22%2C%22TicketId%22%3A%22CPN2508242110000841%22%2C%22State%22%3A%22401%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2508171810001617%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=NzExODc1OWY2YWE3Njg1MTU4M2JkOTBmODI0NjJkYTA%3D&RequestType=103`

	fmt.Println("🔍 真实数据配置：")
	fmt.Println("   📋 订单号: GK20250817000004210")
	fmt.Println("   🎫 工单ID: CPN2508242110000841")
	fmt.Println("   🔗 快递鸟订单号: KDN2508171810001617")
	fmt.Println("   📦 运单号: 773372416148735")
	fmt.Println("   ⚡ 回调State: 401 (工单回调)")
	fmt.Println("   🏢 供应商: 快递鸟")
	fmt.Println("   📅 数据来源: 数据库真实记录")
	fmt.Println("   🕐 接收时间: 2025-08-24 13:35:40")
	fmt.Println()

	fmt.Println("🚀 开始真实数据验证测试...")
	fmt.Println("📊 完整处理流程：")
	fmt.Println("   1️⃣ 回调接收：✅ 接收真实快递鸟工单回调")
	fmt.Println("   2️⃣ 数据解析：✅ 解析URL编码，提取KDNOrderCode")
	fmt.Println("   3️⃣ 状态识别：✅ 识别State=401为工单回调")
	fmt.Println("   4️⃣ API调用：🔥 调用快递鸟工单详情查询API")
	fmt.Println("   5️⃣ 真实状态：🔥 获取KDN2508171810001617的真实状态")
	fmt.Println("   6️⃣ 状态映射：🔥 API状态 → 统一状态映射")
	fmt.Println("   7️⃣ 内部处理：✅ 更新工单状态到数据库")
	fmt.Println("   8️⃣ 事件匹配：✅ workorder.* 匹配 workorder_updated")
	fmt.Println("   9️⃣ 外部转发：✅ 转发真实状态到用户回调URL")
	fmt.Println("   🔟 转发记录：✅ 保存转发记录到数据库")
	fmt.Println()

	// 发送真实的快递鸟工单回调
	fmt.Println("🔥 发送真实快递鸟工单回调到主系统...")
	success := testRealKuaidiniaoWorkorderCallback("真实快递鸟工单回调验证", "kuaidiniao", realKuaidiniaoWorkorderCallback)

	if success {
		fmt.Println("\n⏳ 等待20秒，让系统完整处理（包括API调用和状态映射）...")
		time.Sleep(20 * time.Second)

		fmt.Println("🔍 验证处理结果：")
		fmt.Println("   ✅ 请检查主系统日志确认以下关键信息：")
		fmt.Println()
		fmt.Println("   📥 【回调接收】")
		fmt.Println("      - 应显示: '接收工单回调'")
		fmt.Println("      - 应显示: 'provider=kuaidiniao'")
		fmt.Println()
		fmt.Println("   🔧 【数据解析】")
		fmt.Println("      - 应显示: '解析快递鸟工单回调'")
		fmt.Println("      - 应显示: 'KDNOrderCode=KDN2508171810001617'")
		fmt.Println()
		fmt.Println("   🔍 【状态查询】")
		fmt.Println("      - 应显示: '开始查询快递鸟工单真实状态'")
		fmt.Println("      - 应显示: '快递鸟工单查询开始'")
		fmt.Println("      - 应显示: 调用快递鸟工单详情查询API")
		fmt.Println()
		fmt.Println("   📊 【API响应】")
		fmt.Println("      - 如果工单存在: 应显示'从快递鸟工单详情API获取到真实状态'")
		fmt.Println("      - 如果工单不存在: 应显示'未找到工单'或API错误")
		fmt.Println("      - 应显示: API状态映射到统一状态")
		fmt.Println()
		fmt.Println("   🔄 【状态映射】")
		fmt.Println("      - 快递鸟API: 0=待处理, 1=处理中, 2=已处理")
		fmt.Println("      - 统一状态: 1=待处理, 2=处理中, 3=已处理")
		fmt.Println("      - 应显示: 'api_status' 和 'unified_status'")
		fmt.Println()
		fmt.Println("   📝 【内部处理】")
		fmt.Println("      - 应显示: '开始内部工单处理'")
		fmt.Println("      - 应显示: 状态映射查询和更新")
		fmt.Println()
		fmt.Println("   🎯 【事件匹配】")
		fmt.Println("      - 应显示: 用户订阅了此事件")
		fmt.Println("      - 应显示: 'workorder.*' 匹配 'workorder_updated'")
		fmt.Println()
		fmt.Println("   🔄 【外部转发】")
		fmt.Println("      - 应显示: '工单回调转发完成'")
		fmt.Println("      - 应显示: 转发到 'https://qq.py258.com/app-api/callback/open'")
		fmt.Println("      - 应显示: HTTP状态码和响应")
		fmt.Println()
		fmt.Println("   📋 【转发记录】")
		fmt.Println("      - 应显示: '转发记录保存成功'")
		fmt.Println("      - 应显示: 记录ID和状态")
		fmt.Println()
		fmt.Println("   ❌ 【错误检查】")
		fmt.Println("      - 不应出现任何处理错误")
		fmt.Println("      - 如果API查询失败，应有明确的错误信息")
		fmt.Println()
	}

	fmt.Println("=== 真实快递鸟工单回调验证完成 ===")
	fmt.Println("🎯 验证目标：")
	fmt.Println("   ✅ 确认真实数据能触发完整的API查询链路")
	fmt.Println("   ✅ 验证快递鸟工单详情查询API调用")
	fmt.Println("   ✅ 确认状态映射逻辑正确执行")
	fmt.Println("   ✅ 验证外部转发的状态是真实状态")
	fmt.Println("   ✅ 确认完整的企业级处理链路")
	fmt.Println()
	fmt.Println("📋 成功标准：")
	fmt.Println("   🔍 API调用成功：成功调用快递鸟工单详情查询接口")
	fmt.Println("   📊 状态获取正确：获取到真实的工单状态")
	fmt.Println("   🔄 映射逻辑正确：API状态正确映射到统一状态")
	fmt.Println("   📝 转发数据准确：外部转发的状态反映真实状态")
	fmt.Println("   🏢 企业级质量：完整的错误处理和日志记录")
	fmt.Println("   ❌ 零错误处理：整个链路不出现任何错误")
}

func testRealKuaidiniaoWorkorderCallback(name, provider, data string) bool {
	// 🔥 直接测试主系统统一回调端点
	mainSystemURL := fmt.Sprintf("http://localhost:8081/api/v1/callbacks/%s", provider)
	
	req, err := http.NewRequest("POST", mainSystemURL, bytes.NewBufferString(data))
	if err != nil {
		fmt.Printf("❌ %s - 创建请求失败: %v\n", name, err)
		return false
	}

	// 设置请求头（模拟真实的快递鸟工单回调）
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Apache-HttpClient/4.5.1 (Java/1.8.0_131)")
	req.Header.Set("Accept-Encoding", "gzip,deflate")
	req.Header.Set("Connection", "Keep-Alive")

	client := &http.Client{Timeout: 45 * time.Second} // 增加超时时间，因为要调用API
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ %s - 发送请求失败: %v\n", name, err)
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ %s - 读取响应失败: %v\n", name, err)
		return false
	}

	fmt.Printf("✅ %s\n", name)
	fmt.Printf("   🎯 主系统URL: %s\n", mainSystemURL)
	fmt.Printf("   📊 状态码: %d\n", resp.StatusCode)
	fmt.Printf("   📝 响应: %s\n", string(body))
	fmt.Printf("   🔄 处理流程: 真实数据 -> API查询 -> 状态映射 -> 外部转发\n")
	fmt.Printf("   📋 数据来源: 数据库真实记录\n")
	fmt.Printf("   🎯 期望结果: 完整的API调用链路和状态转发\n")

	return resp.StatusCode == 200
}
