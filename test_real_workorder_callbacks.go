package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 真实回调测试数据
type RealCallbackTest struct {
	Name        string
	Provider    string
	RawBody     string
	Description string
}

func main() {
	fmt.Println("=== 真实工单回调测试 ===")
	fmt.Println("🔥 使用从数据库提取的真实回调数据")
	fmt.Println("📋 验证工单回复记录保存功能")
	fmt.Println()

	// 真实的快递鸟工单回调数据
	kuaidiniaoTests := []RealCallbackTest{
		{
			Name:        "快递鸟工单1 - 代收签收",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-22+20%3A22%3A09%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22LogisticCode%22%3A%22773373330843280%22%2C%22ShipperCode%22%3A%22STO%22%2C%22State%22%3A%223%22%2C%22CreateTime%22%3A%222025-08-25+14%3A27%3A24%22%2C%22KDNOrderCode%22%3A%22KDN2508222010000617%22%2C%22OrderCode%22%3A%22GK20250822000004612%22%2C%22Reason%22%3A%22%E3%80%90%E4%BB%A3%E6%94%B6%E7%AD%BE%E6%94%B6%E3%80%91%E6%82%A8%E7%9A%84%E5%8C%85%E8%A3%B9%E5%B7%B2%E9%80%81%E8%B4%A7%E4%B8%8A%E9%97%A8%EF%BC%8C%E6%94%BE%E8%87%B3%E5%AE%B6%E9%97%A8%E5%8F%A3%E3%80%82%E5%A6%82%E6%9C%89%E9%97%AE%E9%A2%98%E5%8F%AF%E8%87%B4%E7%94%B515221870700%E3%80%82%E6%9C%9F%E5%BE%85%E5%86%8D%E6%AC%A1%E4%B8%BA%E6%82%A8%E6%9C%8D%E5%8A%A1%E3%80%82%22%2C%22SignType%22%3A%223001%22%2C%22CallRequestType%22%3A%221801%22%7D%5D%2C%22Count%22%3A1%7D&DataSign=Y2M1MGFjYjFiMDc0NWQ3MjBlZTRiN2YwYjUxMTc1ZDU%3D&RequestType=103",
			Description: "订单号: GK20250822000004612, 运单号: 773373330843280, 状态: 已签收",
		},
		{
			Name:        "快递鸟工单2 - 快递柜签收",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-22+19%3A21%3A03%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22LogisticCode%22%3A%22773373328047893%22%2C%22ShipperCode%22%3A%22STO%22%2C%22State%22%3A%223%22%2C%22CreateTime%22%3A%222025-08-25+14%3A26%3A42%22%2C%22KDNOrderCode%22%3A%22KDN2508221910000578%22%2C%22OrderCode%22%3A%22GK20250822000004379%22%2C%22Reason%22%3A%22%E3%80%90%E5%BF%AB%E9%80%92%E6%9F%9C%E6%88%96%E9%A9%BF%E7%AB%99%E7%AD%BE%E6%94%B6%E3%80%91%E5%8C%85%E8%A3%B9%E5%B7%B2%E5%AE%8C%E6%88%90%E7%AD%BE%E6%94%B6%EF%BC%8C%E7%AD%BE%E6%94%B6%E6%96%B9%E5%BC%8F%EF%BC%9A%E5%AE%B6%E9%97%A8%E5%8F%A3%EF%BC%8C%E5%A6%82%E6%9C%89%E7%96%91%E9%97%AE%E8%AF%B7%E7%94%B5%E8%81%9413632755691%EF%BC%8C%E6%93%8D%E4%BD%9C%E6%96%B9%E6%B7%B1%E5%9C%B3%E7%BD%97%E6%B9%96%E6%B7%B1%E4%B8%9A%E4%B8%9C%E5%B2%AD6%E6%A0%8B60%E5%8F%B7%E5%BA%97%E8%8F%9C%E9%B8%9F%E9%A9%BF%E7%AB%99%E3%80%82%22%2C%22SignType%22%3A%223001%22%2C%22CallRequestType%22%3A%221801%22%7D%5D%2C%22Count%22%3A1%7D&DataSign=M2NiYmNmNzk5ZDA3MTcxNTE2MzkxYWZjYzA4YTQxMTY%3D&RequestType=103",
			Description: "订单号: GK20250822000004379, 运单号: 773373328047893, 状态: 已签收",
		},
		{
			Name:        "快递鸟工单3 - 自提柜签收",
			Provider:    "kuaidiniao",
			RawBody:     "RequestData=%7B%22PushTime%22%3A%222025-08-22+13%3A53%3A28%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22LogisticCode%22%3A%22773373269304021%22%2C%22ShipperCode%22%3A%22STO%22%2C%22State%22%3A%223%22%2C%22CreateTime%22%3A%222025-08-25+14%3A27%3A52%22%2C%22KDNOrderCode%22%3A%22KDN2508221310002185%22%2C%22OrderCode%22%3A%22GK20250822000002245%22%2C%22Reason%22%3A%22%E3%80%90%E6%B4%BE%E4%BB%B6%E5%BC%82%E5%B8%B8%E5%90%8E%E6%9C%80%E7%BB%88%E7%AD%BE%E6%94%B6%E3%80%91%E3%80%90%E8%87%AA%E6%8F%90%E6%9F%9C%E3%80%91%E5%8C%85%E8%A3%B9%E5%B7%B2%E7%AD%BE%E6%94%B6%EF%BC%81%E5%A6%82%E6%9C%89%E9%97%AE%E9%A2%98%E8%AF%B7%E8%81%94%E7%B3%BB%E6%B4%BE%E4%BB%B6%E5%91%98%EF%BC%9A13507004083%EF%BC%8C%E6%82%A8%E7%9A%84%E5%BF%AB%E9%80%92%E5%B7%B2%E7%BB%8F%E5%A6%A5%E6%8A%95%EF%BC%8C%E6%8A%95%E8%AF%89%E7%94%B5%E8%AF%9D%EF%BC%9A079182327312%22%2C%22SignType%22%3A%223001%22%2C%22CallRequestType%22%3A%221801%22%7D%5D%2C%22Count%22%3A1%7D&DataSign=NzhjZGRmMTBkMDAwY2NmNGUyYzI5ZTcyMWJmNTBkMDU%3D&RequestType=103",
			Description: "订单号: GK20250822000002245, 运单号: 773373269304021, 状态: 已签收",
		},
	}

	// 真实的快递100工单回调数据
	kuaidi100Tests := []RealCallbackTest{
		{
			Name:        "快递100工单1 - 已签收",
			Provider:    "kuaidi100",
			RawBody:     "param=%7B%22data%22%3A%7B%22orderId%22%3A%22302338611%22%2C%22courierMobile%22%3A%2215154118565%22%2C%22freight%22%3A%224.29%22%2C%22weight%22%3A%221.00%22%2C%22pollToken%22%3A%22JeJR2%2BSr%2BWbIFYonZYs61DoeiI6LuUQuSl%2FDuHu45c4%3D%22%2C%22updateTime%22%3A1756103349869%2C%22sentStatus%22%3A13%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E8%8C%83%E7%9C%9F%E7%9C%9F%22%2C%22defPrice%22%3A%2212.00%22%2C%22thirdOrderId%22%3A%22GK20250822000000883%22%2C%22payStatus%22%3A0%2C%22status%22%3A13%7D%2C%22kuaidicom%22%3A%22yunda%22%2C%22kuaidinum%22%3A%22312870663311871%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=50416CA85ED3804805E66B4BD54709F2&taskId=493AD8827EA04239339FA16BA5EE6825",
			Description: "订单号: GK20250822000000883, 运单号: 312870663311871, 状态: 已签收",
		},
		{
			Name:        "快递100工单2 - 已揽收",
			Provider:    "kuaidi100",
			RawBody:     "param=%7B%22data%22%3A%7B%22orderId%22%3A%22302986323%22%2C%22courierMobile%22%3A%2213628577031%22%2C%22freight%22%3Anull%2C%22weight%22%3Anull%2C%22pollToken%22%3A%22C0rcYv0C%2F4NaG%2BB0k66VJdwtcga5k8MOHnowA4XgyJY%3D%22%2C%22updateTime%22%3A1756103322400%2C%22sentStatus%22%3A10%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E4%BB%98%E9%94%A1%E6%B4%8B%22%2C%22defPrice%22%3Anull%2C%22thirdOrderId%22%3A%22GK20250824000002343%22%2C%22payStatus%22%3A0%2C%22status%22%3A10%7D%2C%22kuaidicom%22%3A%22yunda%22%2C%22kuaidinum%22%3A%22312873113816864%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=84BA6D309CEDF0F858C3677E5A027E9D&taskId=FD75CC40C7E87332E5BA58CCE83DFF7E",
			Description: "订单号: GK20250824000002343, 运单号: 312873113816864, 状态: 已揽收",
		},
		{
			Name:        "快递100工单3 - 已下单",
			Provider:    "kuaidi100",
			RawBody:     "param=%7B%22data%22%3A%7B%22orderId%22%3A%22303275707%22%2C%22courierMobile%22%3A%2218427044270%22%2C%22freight%22%3Anull%2C%22weight%22%3Anull%2C%22pollToken%22%3A%225SpJ63Q0rgDDmANsT0F4GjiO1dIp91GGIt5r0i9zX14%3D%22%2C%22updateTime%22%3A1756103319440%2C%22sentStatus%22%3A1%2C%22userCancel%22%3Afalse%2C%22courierName%22%3A%22%E9%98%B3%E4%BA%89%E5%85%89%22%2C%22defPrice%22%3Anull%2C%22thirdOrderId%22%3A%22GK20250825000000004%22%2C%22pickupCode%22%3A%223576%22%2C%22payStatus%22%3A0%2C%22extraInfo%22%3A%22%7B%5C%22bulkpen%5C%22%3A%5C%22%5C%22%7D%22%2C%22status%22%3A1%7D%2C%22kuaidicom%22%3A%22yunda%22%2C%22kuaidinum%22%3A%22434741624705062%22%2C%22message%22%3A%22%E6%88%90%E5%8A%9F%22%2C%22status%22%3A%22200%22%7D&sign=4C2F7AA5C31DF78BD132A8E2F61754BA&taskId=A8895459EEA01CF7709291B5EFDA5D7D",
			Description: "订单号: GK20250825000000004, 运单号: 434741624705062, 状态: 已下单",
		},
	}

	fmt.Println("🔍 准备测试 6 个真实工单回调:")
	fmt.Println("   📦 快递鸟工单: 3个")
	fmt.Println("   📦 快递100工单: 3个")
	fmt.Println()

	// 测试快递鸟工单回调
	fmt.Println("🚀 开始测试快递鸟工单回调...")
	for i, test := range kuaidiniaoTests {
		fmt.Printf("\n🔥 测试 %d/3: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)

		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}

		if i < len(kuaidiniaoTests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")

	// 测试快递100工单回调
	fmt.Println("🚀 开始测试快递100工单回调...")
	for i, test := range kuaidi100Tests {
		fmt.Printf("\n🔥 测试 %d/3: %s\n", i+1, test.Name)
		fmt.Printf("   📋 %s\n", test.Description)

		success := sendCallback(test.Provider, test.RawBody)
		if success {
			fmt.Printf("   ✅ 测试成功\n")
		} else {
			fmt.Printf("   ❌ 测试失败\n")
		}

		if i < len(kuaidi100Tests)-1 {
			fmt.Printf("\n⏳ 等待2秒...\n")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Println("=== 真实工单回调测试完成 ===")
	fmt.Println("📋 请检查主系统日志确认:")
	fmt.Println("   🔥 每个工单都能正确解析和处理")
	fmt.Println("   🎯 每个工单都能找到对应的工单记录")
	fmt.Println("   📊 每个工单都有完整的回复记录保存")
	fmt.Println("   🔄 每个工单都有状态映射和外部转发记录")
}

// sendCallback 发送回调请求
func sendCallback(provider, rawBody string) bool {
	// 构建URL
	url := fmt.Sprintf("http://localhost:8081/api/v1/callbacks/%s", provider)

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(rawBody))
	if err != nil {
		fmt.Printf("   ❌ 创建请求失败: %v\n", err)
		return false
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   ❌ 发送请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ 读取响应失败: %v\n", err)
		return false
	}

	fmt.Printf("   📊 HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   📝 响应内容: %s\n", string(body))

	return resp.StatusCode == 200
}
